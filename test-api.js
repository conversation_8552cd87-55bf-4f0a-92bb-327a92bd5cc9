// Simple test script to verify the API is working
// Run with: node test-api.js
// Make sure your API is running first with: npm run start

const API_BASE = 'http://localhost:8787/api';

async function testAPI() {
  console.log('🧪 Testing Series Management API...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing health check...');
    const healthResponse = await fetch('http://localhost:8787/');
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData.message);
    console.log('📋 Available endpoints:', Object.keys(healthData.endpoints).join(', '));

    // Test 2: Get all series (should be empty initially)
    console.log('\n2. Testing GET /api/series...');
    const getAllResponse = await fetch(`${API_BASE}/series`);
    const getAllData = await getAllResponse.json();
    console.log('✅ Get all series:', getAllData.success ? 'Success' : 'Failed');
    console.log('📊 Total series:', getAllData.data?.pagination?.total || 0);

    // Test 3: Create a new series
    console.log('\n3. Testing POST /api/series...');
    const createResponse = await fetch(`${API_BASE}/series`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Test Series ' + Date.now(),
        chapter: 100,
        status: 'Reading'
      }),
    });
    const createData = await createResponse.json();
    console.log('✅ Create series:', createData.success ? 'Success' : 'Failed');
    
    if (createData.success) {
      const seriesId = createData.data.id;
      console.log('🆔 Created series ID:', seriesId);

      // Test 4: Get series by ID
      console.log('\n4. Testing GET /api/series/:id...');
      const getByIdResponse = await fetch(`${API_BASE}/series/${seriesId}`);
      const getByIdData = await getByIdResponse.json();
      console.log('✅ Get series by ID:', getByIdData.success ? 'Success' : 'Failed');

      // Test 5: Update series
      console.log('\n5. Testing PUT /api/series/:id...');
      const updateResponse = await fetch(`${API_BASE}/series/${seriesId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chapter: 150,
          status: 'Completed'
        }),
      });
      const updateData = await updateResponse.json();
      console.log('✅ Update series:', updateData.success ? 'Success' : 'Failed');

      // Test 6: Delete series
      console.log('\n6. Testing DELETE /api/series/:id...');
      const deleteResponse = await fetch(`${API_BASE}/series/${seriesId}`, {
        method: 'DELETE',
      });
      const deleteData = await deleteResponse.json();
      console.log('✅ Delete series:', deleteData.success ? 'Success' : 'Failed');
    }

    // Test 7: Test pagination
    console.log('\n7. Testing pagination...');
    const paginationResponse = await fetch(`${API_BASE}/series?page=1&limit=5&sort=name&order=asc`);
    const paginationData = await paginationResponse.json();
    console.log('✅ Pagination test:', paginationData.success ? 'Success' : 'Failed');

    // Test 8: Test validation error
    console.log('\n8. Testing validation error...');
    const validationResponse = await fetch(`${API_BASE}/series`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: '', // Invalid: empty name
        chapter: -5 // Invalid: negative chapter
      }),
    });
    const validationData = await validationResponse.json();
    console.log('✅ Validation error test:', !validationData.success ? 'Success (correctly rejected)' : 'Failed');

    console.log('\n🎉 All tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n💡 Make sure your API is running with: npm run start');
    console.log('💡 And your DATABASE_URL is configured in .dev.vars');
  }
}

// Run the tests
testAPI();
