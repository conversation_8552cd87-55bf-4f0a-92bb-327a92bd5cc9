# Series Management API

A RESTful API built with Hono.js for managing a series database with CRUD operations and pagination support.

## Features

- ✅ Full CRUD operations (Create, Read, Update, Delete)
- ✅ Pagination with customizable page size
- ✅ Filtering by status and search by name
- ✅ Sorting by multiple fields (name, chapter, status, updated_at)
- ✅ Input validation with Zod schemas
- ✅ PostgreSQL database integration
- ✅ TypeScript support
- ✅ Error handling and proper HTTP status codes
- ✅ CORS support

## Database Schema

The API works with a PostgreSQL table called `series` with the following structure:

```sql
CREATE TABLE series (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    name text COLLATE pg_catalog."default" NOT NULL,
    chapter numeric,
    status text COLLATE pg_catalog."default",
    updated_at timestamp without time zone DEFAULT now(),
    CONSTRAINT series_pkey PRIMARY KEY (id),
    CONSTRAINT unique_series_name UNIQUE (name),
    CONSTRAINT series_status_check CHECK (status = ANY (ARRAY['Reading'::text, 'Completed'::text, 'On-Hold'::text, 'Dropped'::text, 'Cancelled'::text, 'Plan to Read'::text]))
);
```

## Setup

1. Install dependencies:
```bash
npm install
```

2. Set up your database URL in `wrangler.jsonc`:
```json
{
  "vars": {
    "DATABASE_URL": "your_postgresql_connection_string"
  }
}
```

3. For local development, create a `.dev.vars` file:
```
DATABASE_URL=your_postgresql_connection_string
```

4. Start the development server:
```bash
npm run start
```

5. Deploy to Cloudflare Workers:
```bash
npm run deploy
```

## API Endpoints

### Health Check
- **GET** `/` - API health check and endpoint documentation

### Series Management

#### Get All Series (with pagination)
- **GET** `/api/series`

**Query Parameters:**
- `page` (number, default: 1) - Page number
- `limit` (number, default: 10, max: 100) - Items per page
- `sort` (string, default: "updated_at") - Sort field: name, chapter, status, updated_at
- `order` (string, default: "desc") - Sort order: asc, desc
- `status` (string, optional) - Filter by status: Reading, Completed, On-Hold, Dropped, Cancelled, Plan to Read
- `search` (string, optional) - Search by series name (case-insensitive)

**Example:**
```
GET /api/series?page=1&limit=20&sort=name&order=asc&status=Reading&search=naruto
```

#### Get Series by ID
- **GET** `/api/series/:id`

#### Create New Series
- **POST** `/api/series`

**Request Body:**
```json
{
  "name": "Series Name",
  "chapter": 150,
  "status": "Reading"
}
```

#### Update Series
- **PUT** `/api/series/:id`

**Request Body (all fields optional):**
```json
{
  "name": "Updated Series Name",
  "chapter": 200,
  "status": "Completed"
}
```

#### Delete Series
- **DELETE** `/api/series/:id`

## Response Format

All API responses follow this format:

```json
{
  "success": boolean,
  "data": any,
  "error": string,
  "message": string
}
```

### Paginated Response Format

```json
{
  "success": true,
  "data": {
    "data": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "totalPages": 10,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

## Status Values

The following status values are supported:
- `Reading`
- `Completed`
- `On-Hold`
- `Dropped`
- `Cancelled`
- `Plan to Read`

## Development

For generating/synchronizing types based on your Worker configuration:

```bash
npm run cf-typegen
```

## Error Handling

The API returns appropriate HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `404` - Not Found
- `409` - Conflict (duplicate name)
- `500` - Internal Server Error
