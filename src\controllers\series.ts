import { Context } from "hono";
import { DatabaseService } from "../services/database";
import {
  CreateSeriesSchema,
  UpdateSeriesSchema,
  PaginationSchema,
  ApiResponse,
  Series,
  PaginatedResponse,
} from "../types/series";

type CloudflareBindings = {
  DATABASE_URL: string;
};

export class SeriesController {
  private getDbService(
    c: Context<{ Bindings: CloudflareBindings }>
  ): DatabaseService {
    return new DatabaseService(c.env.DATABASE_URL);
  }

  async getAllSeries(c: Context<{ Bindings: CloudflareBindings }>) {
    try {
      // Validate query parameters
      const queryResult = PaginationSchema.safeParse({
        page: c.req.query("page"),
        limit: c.req.query("limit"),
        sort: c.req.query("sort"),
        order: c.req.query("order"),
        status: c.req.query("status"),
        search: c.req.query("search"),
      });

      if (!queryResult.success) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Invalid query parameters",
            data: null,
          },
          400
        );
      }

      const db = this.getDbService(c);
      const result = await db.getAllSeries(queryResult.data);

      return c.json<ApiResponse<PaginatedResponse<Series>>>({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error("Error fetching series:", error);
      return c.json<ApiResponse<null>>(
        {
          success: false,
          error: "Internal server error",
        },
        500
      );
    }
  }

  async getSeriesById(c: Context<{ Bindings: CloudflareBindings }>) {
    try {
      const id = c.req.param("id");

      if (!id) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Series ID is required",
          },
          400
        );
      }

      const db = this.getDbService(c);
      const series = await db.getSeriesById(id);

      if (!series) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Series not found",
          },
          404
        );
      }

      return c.json<ApiResponse<Series>>({
        success: true,
        data: series,
      });
    } catch (error) {
      console.error("Error fetching series by ID:", error);
      return c.json<ApiResponse<null>>(
        {
          success: false,
          error: "Internal server error",
        },
        500
      );
    }
  }

  async createSeries(c: Context<{ Bindings: CloudflareBindings }>) {
    try {
      const body = await c.req.json();

      // Validate request body
      const validationResult = CreateSeriesSchema.safeParse(body);
      if (!validationResult.success) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Invalid request data",
            data: validationResult.error.errors,
          },
          400
        );
      }

      const db = this.getDbService(c);

      // Check if series with same name already exists
      const exists = await db.seriesExists(validationResult.data.name);
      if (exists) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Series with this name already exists",
          },
          409
        );
      }

      const series = await db.createSeries(validationResult.data);

      return c.json<ApiResponse<Series>>(
        {
          success: true,
          data: series,
          message: "Series created successfully",
        },
        201
      );
    } catch (error) {
      console.error("Error creating series:", error);
      return c.json<ApiResponse<null>>(
        {
          success: false,
          error: "Internal server error",
        },
        500
      );
    }
  }

  async updateSeries(c: Context<{ Bindings: CloudflareBindings }>) {
    try {
      const id = c.req.param("id");
      const body = await c.req.json();

      if (!id) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Series ID is required",
          },
          400
        );
      }

      // Validate request body
      const validationResult = UpdateSeriesSchema.safeParse(body);
      if (!validationResult.success) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Invalid request data",
            data: validationResult.error.errors,
          },
          400
        );
      }

      const db = this.getDbService(c);

      // Check if series exists
      const existingSeries = await db.getSeriesById(id);
      if (!existingSeries) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Series not found",
          },
          404
        );
      }

      // Check if name is being updated and if it conflicts with existing series
      if (validationResult.data.name) {
        const nameExists = await db.seriesExists(
          validationResult.data.name,
          id
        );
        if (nameExists) {
          return c.json<ApiResponse<null>>(
            {
              success: false,
              error: "Series with this name already exists",
            },
            409
          );
        }
      }

      const updatedSeries = await db.updateSeries(id, validationResult.data);

      return c.json<ApiResponse<Series>>({
        success: true,
        data: updatedSeries!,
        message: "Series updated successfully",
      });
    } catch (error) {
      console.error("Error updating series:", error);
      return c.json<ApiResponse<null>>(
        {
          success: false,
          error: "Internal server error",
        },
        500
      );
    }
  }

  async deleteSeries(c: Context<{ Bindings: CloudflareBindings }>) {
    try {
      const id = c.req.param("id");

      if (!id) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Series ID is required",
          },
          400
        );
      }

      const db = this.getDbService(c);

      // Check if series exists
      const existingSeries = await db.getSeriesById(id);
      if (!existingSeries) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Series not found",
          },
          404
        );
      }

      const deleted = await db.deleteSeries(id);

      if (!deleted) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Failed to delete series",
          },
          500
        );
      }

      return c.json<ApiResponse<null>>({
        success: true,
        message: "Series deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting series:", error);
      return c.json<ApiResponse<null>>(
        {
          success: false,
          error: "Internal server error",
        },
        500
      );
    }
  }
}
