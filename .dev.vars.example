# Copy this file to .dev.vars and fill in your actual database URL
# This file is used for local development with Wrangler

# PostgreSQL connection string
# Format: postgresql://username:password@host:port/database
# Example: postgresql://user:password@localhost:5432/series_db
DATABASE_URL=postgresql://username:password@host:port/database_name

# For production deployment, set this in your Cloudflare Workers environment variables
# or update the wrangler.jsonc file with your production database URL
