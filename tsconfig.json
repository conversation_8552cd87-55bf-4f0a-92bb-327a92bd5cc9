{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "skipLibCheck": true, "lib": ["ESNext"], "types": ["vite/client", "jest", "@types/jest"], "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true}, "include": ["src/**/*", "tests/**/*"], "exclude": ["node_modules", "dist"]}