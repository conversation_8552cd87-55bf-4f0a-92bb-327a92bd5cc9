// Test delete endpoint
// Run with: node test-delete.js

async function testDelete() {
  try {
    console.log('🧪 Testing DELETE endpoint...\n');
    
    // First create a test series to delete
    console.log('1. Creating a test series...');
    const createResponse = await fetch('http://localhost:5174/api/series', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Test Series for Deletion ' + Date.now(),
        chapter: 1,
        status: 'Reading'
      }),
    });
    
    const createData = await createResponse.json();
    
    if (createData.success) {
      const seriesId = createData.data.id;
      const seriesName = createData.data.name;
      console.log('✅ Created test series:', seriesName, 'with ID:', seriesId);
      
      // Verify it exists
      console.log('\n2. Verifying series exists...');
      const getResponse = await fetch(`http://localhost:5174/api/series/${seriesId}`);
      const getData = await getResponse.json();
      
      if (getData.success) {
        console.log('✅ Series exists:', getData.data.name);
        
        // Now delete it
        console.log('\n3. Deleting the series...');
        const deleteResponse = await fetch(`http://localhost:5174/api/series/${seriesId}`, {
          method: 'DELETE',
        });
        
        const deleteData = await deleteResponse.json();
        console.log('📊 Delete response status:', deleteResponse.status);
        console.log('📋 Delete response data:', JSON.stringify(deleteData, null, 2));
        
        // Verify it's actually deleted
        console.log('\n4. Verifying series is deleted...');
        const verifyResponse = await fetch(`http://localhost:5174/api/series/${seriesId}`);
        const verifyData = await verifyResponse.json();
        
        if (verifyResponse.status === 404 || !verifyData.success) {
          console.log('✅ Series successfully deleted from database');
          
          if (deleteData.success) {
            console.log('🎉 Delete endpoint is working correctly!');
          } else {
            console.log('⚠️  Series was deleted but endpoint returned error');
          }
        } else {
          console.log('❌ Series still exists in database');
        }
        
      } else {
        console.log('❌ Failed to verify series exists');
      }
      
    } else {
      console.log('❌ Failed to create test series:', createData.error);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testDelete();
