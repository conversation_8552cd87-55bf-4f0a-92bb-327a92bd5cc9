import { SeriesController } from '../../src/controllers/series';
import { DatabaseService } from '../../src/services/database';
import { SeriesStatus } from '../../src/types/series';

// Mock the database service
jest.mock('../../src/services/database');

// Mock Hono context
const createMockContext = (overrides: any = {}) => ({
  env: {
    DATABASE_URL: 'test://connection',
    ...overrides.env,
  },
  req: {
    json: jest.fn(),
    query: jest.fn(),
    param: jest.fn(),
    ...overrides.req,
  },
  json: jest.fn(),
  ...overrides,
});

describe('SeriesController', () => {
  let controller: SeriesController;
  let mockDatabaseService: jest.Mocked<DatabaseService>;

  beforeEach(() => {
    jest.clearAllMocks();
    controller = new SeriesController();
    
    // Create mock database service instance
    mockDatabaseService = {
      getAllSeries: jest.fn(),
      getSeriesById: jest.fn(),
      createSeries: jest.fn(),
      updateSeries: jest.fn(),
      deleteSeries: jest.fn(),
      seriesExists: jest.fn(),
    } as any;

    // Mock the DatabaseService constructor
    (DatabaseService as jest.Mock).mockImplementation(() => mockDatabaseService);
  });

  describe('getAllSeries', () => {
    it('should return paginated series successfully', async () => {
      const mockContext = createMockContext({
        req: {
          query: jest.fn((key: string) => {
            const params: Record<string, string> = {
              page: '1',
              limit: '10',
              sort: 'updated_at',
              order: 'desc',
            };
            return params[key];
          }),
        },
      });

      const mockResult = {
        data: [
          {
            id: '123e4567-e89b-12d3-a456-426614174000',
            name: 'Test Series',
            chapter: 100,
            status: SeriesStatus.READING,
            updated_at: new Date('2024-01-01'),
          },
        ],
        pagination: {
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      };

      mockDatabaseService.getAllSeries.mockResolvedValue(mockResult);

      await controller.getAllSeries(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith({
        success: true,
        data: mockResult,
      });
    });

    it('should return validation error for invalid query parameters', async () => {
      const mockContext = createMockContext({
        req: {
          query: jest.fn((key: string) => {
            const params: Record<string, string> = {
              page: '0', // Invalid page
              limit: '10',
            };
            return params[key];
          }),
        },
      });

      await controller.getAllSeries(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith(
        {
          success: false,
          error: 'Invalid query parameters',
          data: null,
        },
        400
      );
    });

    it('should handle database errors', async () => {
      const mockContext = createMockContext({
        req: {
          query: jest.fn(() => undefined),
        },
      });

      mockDatabaseService.getAllSeries.mockRejectedValue(new Error('Database error'));

      await controller.getAllSeries(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith(
        {
          success: false,
          error: 'Internal server error',
        },
        500
      );
    });
  });

  describe('getSeriesById', () => {
    it('should return series when found', async () => {
      const mockContext = createMockContext({
        req: {
          param: jest.fn(() => '123e4567-e89b-12d3-a456-426614174000'),
        },
      });

      const mockSeries = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Test Series',
        chapter: 100,
        status: SeriesStatus.READING,
        updated_at: new Date('2024-01-01'),
      };

      mockDatabaseService.getSeriesById.mockResolvedValue(mockSeries);

      await controller.getSeriesById(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith({
        success: true,
        data: mockSeries,
      });
    });

    it('should return 404 when series not found', async () => {
      const mockContext = createMockContext({
        req: {
          param: jest.fn(() => 'non-existent-id'),
        },
      });

      mockDatabaseService.getSeriesById.mockResolvedValue(null);

      await controller.getSeriesById(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith(
        {
          success: false,
          error: 'Series not found',
        },
        404
      );
    });

    it('should return 400 when ID is missing', async () => {
      const mockContext = createMockContext({
        req: {
          param: jest.fn(() => undefined),
        },
      });

      await controller.getSeriesById(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith(
        {
          success: false,
          error: 'Series ID is required',
        },
        400
      );
    });
  });

  describe('createSeries', () => {
    it('should create series successfully', async () => {
      const mockContext = createMockContext({
        req: {
          json: jest.fn().mockResolvedValue({
            name: 'New Series',
            chapter: 1,
            status: SeriesStatus.READING,
          }),
        },
      });

      const mockCreatedSeries = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'New Series',
        chapter: 1,
        status: SeriesStatus.READING,
        updated_at: new Date('2024-01-01'),
      };

      mockDatabaseService.seriesExists.mockResolvedValue(false);
      mockDatabaseService.createSeries.mockResolvedValue(mockCreatedSeries);

      await controller.createSeries(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith(
        {
          success: true,
          data: mockCreatedSeries,
          message: 'Series created successfully',
        },
        201
      );
    });

    it('should return validation error for invalid data', async () => {
      const mockContext = createMockContext({
        req: {
          json: jest.fn().mockResolvedValue({
            name: '', // Invalid empty name
            chapter: -5, // Invalid negative chapter
          }),
        },
      });

      await controller.createSeries(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Invalid request data',
        }),
        400
      );
    });

    it('should return conflict error when series name already exists', async () => {
      const mockContext = createMockContext({
        req: {
          json: jest.fn().mockResolvedValue({
            name: 'Existing Series',
            chapter: 1,
            status: SeriesStatus.READING,
          }),
        },
      });

      mockDatabaseService.seriesExists.mockResolvedValue(true);

      await controller.createSeries(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith(
        {
          success: false,
          error: 'Series with this name already exists',
        },
        409
      );
    });
  });

  describe('updateSeries', () => {
    it('should update series successfully', async () => {
      const mockContext = createMockContext({
        req: {
          param: jest.fn(() => '123e4567-e89b-12d3-a456-426614174000'),
          json: jest.fn().mockResolvedValue({
            chapter: 150,
            status: SeriesStatus.COMPLETED,
          }),
        },
      });

      const mockExistingSeries = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Test Series',
        chapter: 100,
        status: SeriesStatus.READING,
        updated_at: new Date('2024-01-01'),
      };

      const mockUpdatedSeries = {
        ...mockExistingSeries,
        chapter: 150,
        status: SeriesStatus.COMPLETED,
        updated_at: new Date('2024-01-02'),
      };

      mockDatabaseService.getSeriesById.mockResolvedValue(mockExistingSeries);
      mockDatabaseService.updateSeries.mockResolvedValue(mockUpdatedSeries);

      await controller.updateSeries(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith({
        success: true,
        data: mockUpdatedSeries,
        message: 'Series updated successfully',
      });
    });

    it('should return 404 when series not found', async () => {
      const mockContext = createMockContext({
        req: {
          param: jest.fn(() => 'non-existent-id'),
          json: jest.fn().mockResolvedValue({
            chapter: 150,
          }),
        },
      });

      mockDatabaseService.getSeriesById.mockResolvedValue(null);

      await controller.updateSeries(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith(
        {
          success: false,
          error: 'Series not found',
        },
        404
      );
    });
  });

  describe('deleteSeries', () => {
    it('should delete series successfully', async () => {
      const mockContext = createMockContext({
        req: {
          param: jest.fn(() => '123e4567-e89b-12d3-a456-426614174000'),
        },
      });

      const mockExistingSeries = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Test Series',
        chapter: 100,
        status: SeriesStatus.READING,
        updated_at: new Date('2024-01-01'),
      };

      mockDatabaseService.getSeriesById.mockResolvedValue(mockExistingSeries);
      mockDatabaseService.deleteSeries.mockResolvedValue(true);

      await controller.deleteSeries(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith({
        success: true,
        message: 'Series deleted successfully',
      });
    });

    it('should return 404 when series not found', async () => {
      const mockContext = createMockContext({
        req: {
          param: jest.fn(() => 'non-existent-id'),
        },
      });

      mockDatabaseService.getSeriesById.mockResolvedValue(null);

      await controller.deleteSeries(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith(
        {
          success: false,
          error: 'Series not found',
        },
        404
      );
    });
  });
});
