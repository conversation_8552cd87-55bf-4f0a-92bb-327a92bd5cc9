import request from 'supertest';
import app from '../../src/index';
import { SeriesStatus } from '../../src/types/series';

// Mock the database service for integration tests
jest.mock('../../src/services/database');

describe('API Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Health Check', () => {
    it('should return API information', async () => {
      const response = await request(app)
        .get('/')
        .expect(200);

      expect(response.body).toEqual({
        message: 'Series API is running',
        version: '1.0.0',
        endpoints: {
          'GET /api/series': 'List all series with pagination',
          'GET /api/series/:id': 'Get series by ID',
          'POST /api/series': 'Create new series',
          'PUT /api/series/:id': 'Update series',
          'DELETE /api/series/:id': 'Delete series',
        },
      });
    });
  });

  describe('CORS', () => {
    it('should include CORS headers', async () => {
      const response = await request(app)
        .options('/api/series')
        .expect(200);

      expect(response.headers['access-control-allow-origin']).toBe('*');
      expect(response.headers['access-control-allow-methods']).toContain('GET');
      expect(response.headers['access-control-allow-methods']).toContain('POST');
      expect(response.headers['access-control-allow-methods']).toContain('PUT');
      expect(response.headers['access-control-allow-methods']).toContain('DELETE');
    });
  });

  describe('404 Handler', () => {
    it('should return 404 for non-existent endpoints', async () => {
      const response = await request(app)
        .get('/non-existent-endpoint')
        .expect(404);

      expect(response.body).toEqual({
        success: false,
        error: 'Endpoint not found',
        message: 'The requested endpoint does not exist',
      });
    });
  });

  describe('Error Handler', () => {
    it('should handle internal server errors gracefully', async () => {
      // Mock the database service to throw an error
      const { DatabaseService } = require('../../src/services/database');
      DatabaseService.mockImplementation(() => ({
        getAllSeries: jest.fn().mockRejectedValue(new Error('Database connection failed')),
      }));

      const response = await request(app)
        .get('/api/series')
        .expect(500);

      expect(response.body).toEqual({
        success: false,
        error: 'Internal server error',
      });
    });
  });

  describe('Request Validation', () => {
    it('should validate pagination parameters', async () => {
      const response = await request(app)
        .get('/api/series?page=0&limit=101')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid query parameters');
    });

    it('should validate create series request body', async () => {
      const response = await request(app)
        .post('/api/series')
        .send({
          name: '', // Invalid empty name
          chapter: -5, // Invalid negative chapter
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid request data');
    });

    it('should validate update series request body', async () => {
      const response = await request(app)
        .put('/api/series/123e4567-e89b-12d3-a456-426614174000')
        .send({
          name: '', // Invalid empty name
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid request data');
    });
  });

  describe('Content-Type Handling', () => {
    it('should require JSON content type for POST requests', async () => {
      const response = await request(app)
        .post('/api/series')
        .send('invalid-json')
        .expect(500); // This will trigger the error handler

      expect(response.body.success).toBe(false);
    });

    it('should accept valid JSON for POST requests', async () => {
      // Mock successful creation
      const { DatabaseService } = require('../../src/services/database');
      DatabaseService.mockImplementation(() => ({
        seriesExists: jest.fn().mockResolvedValue(false),
        createSeries: jest.fn().mockResolvedValue({
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Test Series',
          chapter: 1,
          status: 'Reading',
          updated_at: new Date('2024-01-01'),
        }),
      }));

      const response = await request(app)
        .post('/api/series')
        .send({
          name: 'Test Series',
          chapter: 1,
          status: SeriesStatus.READING,
        })
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe('Test Series');
    });
  });

  describe('HTTP Methods', () => {
    it('should support GET method for listing series', async () => {
      // Mock successful retrieval
      const { DatabaseService } = require('../../src/services/database');
      DatabaseService.mockImplementation(() => ({
        getAllSeries: jest.fn().mockResolvedValue({
          data: [],
          pagination: {
            page: 1,
            limit: 10,
            total: 0,
            totalPages: 0,
            hasNext: false,
            hasPrev: false,
          },
        }),
      }));

      const response = await request(app)
        .get('/api/series')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.data).toEqual([]);
    });

    it('should support GET method for single series', async () => {
      // Mock successful retrieval
      const { DatabaseService } = require('../../src/services/database');
      DatabaseService.mockImplementation(() => ({
        getSeriesById: jest.fn().mockResolvedValue({
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Test Series',
          chapter: 100,
          status: 'Reading',
          updated_at: new Date('2024-01-01'),
        }),
      }));

      const response = await request(app)
        .get('/api/series/123e4567-e89b-12d3-a456-426614174000')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe('Test Series');
    });

    it('should support PUT method for updating series', async () => {
      // Mock successful update
      const { DatabaseService } = require('../../src/services/database');
      DatabaseService.mockImplementation(() => ({
        getSeriesById: jest.fn().mockResolvedValue({
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Test Series',
          chapter: 100,
          status: 'Reading',
          updated_at: new Date('2024-01-01'),
        }),
        seriesExists: jest.fn().mockResolvedValue(false),
        updateSeries: jest.fn().mockResolvedValue({
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Updated Series',
          chapter: 150,
          status: 'Completed',
          updated_at: new Date('2024-01-02'),
        }),
      }));

      const response = await request(app)
        .put('/api/series/123e4567-e89b-12d3-a456-426614174000')
        .send({
          name: 'Updated Series',
          chapter: 150,
          status: SeriesStatus.COMPLETED,
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe('Updated Series');
    });

    it('should support DELETE method for deleting series', async () => {
      // Mock successful deletion
      const { DatabaseService } = require('../../src/services/database');
      DatabaseService.mockImplementation(() => ({
        getSeriesById: jest.fn().mockResolvedValue({
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Test Series',
          chapter: 100,
          status: 'Reading',
          updated_at: new Date('2024-01-01'),
        }),
        deleteSeries: jest.fn().mockResolvedValue(true),
      }));

      const response = await request(app)
        .delete('/api/series/123e4567-e89b-12d3-a456-426614174000')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Series deleted successfully');
    });
  });

  describe('Query Parameter Handling', () => {
    it('should handle pagination query parameters', async () => {
      // Mock successful retrieval with pagination
      const { DatabaseService } = require('../../src/services/database');
      const mockGetAllSeries = jest.fn().mockResolvedValue({
        data: [],
        pagination: {
          page: 2,
          limit: 5,
          total: 20,
          totalPages: 4,
          hasNext: true,
          hasPrev: true,
        },
      });

      DatabaseService.mockImplementation(() => ({
        getAllSeries: mockGetAllSeries,
      }));

      await request(app)
        .get('/api/series?page=2&limit=5&sort=name&order=asc')
        .expect(200);

      expect(mockGetAllSeries).toHaveBeenCalledWith({
        page: 2,
        limit: 5,
        sort: 'name',
        order: 'asc',
      });
    });

    it('should handle filter query parameters', async () => {
      // Mock successful retrieval with filters
      const { DatabaseService } = require('../../src/services/database');
      const mockGetAllSeries = jest.fn().mockResolvedValue({
        data: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 5,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      });

      DatabaseService.mockImplementation(() => ({
        getAllSeries: mockGetAllSeries,
      }));

      await request(app)
        .get('/api/series?status=Reading&search=naruto')
        .expect(200);

      expect(mockGetAllSeries).toHaveBeenCalledWith({
        page: 1,
        limit: 10,
        sort: 'updated_at',
        order: 'desc',
        status: 'Reading',
        search: 'naruto',
      });
    });
  });
});
