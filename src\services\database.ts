import { neon } from "@neondatabase/serverless";
import type {
  Series,
  CreateSeriesInput,
  UpdateSeriesInput,
  PaginationQuery,
  PaginatedResponse,
} from "../types/series";

export class DatabaseService {
  private sql: ReturnType<typeof neon>;

  constructor(databaseUrl: string) {
    this.sql = neon(databaseUrl);
  }

  async getAllSeries(
    query: PaginationQuery
  ): Promise<PaginatedResponse<Series>> {
    const { page, limit, sort, order, status, search } = query;
    const offset = (page - 1) * limit;

    // Build WHERE clause
    const conditions: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    if (status) {
      conditions.push(`status = $${paramIndex}`);
      params.push(status);
      paramIndex++;
    }

    if (search) {
      conditions.push(`name ILIKE $${paramIndex}`);
      params.push(`%${search}%`);
      paramIndex++;
    }

    const whereClause =
      conditions.length > 0 ? `WHERE ${conditions.join(" AND ")}` : "";

    // Get total count
    const countQuery = `SELECT COUNT(*) as count FROM series ${whereClause}`;
    const countResult = await this.sql(countQuery, params);
    const total = parseInt(countResult[0].count);

    // Get paginated data
    const dataQuery = `
      SELECT id, name, chapter, status, updated_at 
      FROM series 
      ${whereClause}
      ORDER BY ${sort} ${order.toUpperCase()}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    const dataParams = [...params, limit, offset];
    const data = await this.sql(dataQuery, dataParams);

    const totalPages = Math.ceil(total / limit);

    return {
      data: data.map((row) => ({
        id: row.id,
        name: row.name,
        chapter: row.chapter,
        status: row.status,
        updated_at: new Date(row.updated_at),
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  async getSeriesById(id: string): Promise<Series | null> {
    const query =
      "SELECT id, name, chapter, status, updated_at FROM series WHERE id = $1";
    const result = await this.sql(query, [id]);

    if (result.length === 0) {
      return null;
    }

    const row = result[0];
    return {
      id: row.id,
      name: row.name,
      chapter: row.chapter,
      status: row.status,
      updated_at: new Date(row.updated_at),
    };
  }

  async createSeries(input: CreateSeriesInput): Promise<Series> {
    const query = `
      INSERT INTO series (name, chapter, status)
      VALUES ($1, $2, $3)
      RETURNING id, name, chapter, status, updated_at
    `;

    const result = await this.sql(query, [
      input.name,
      input.chapter,
      input.status,
    ]);
    const row = result[0];

    return {
      id: row.id,
      name: row.name,
      chapter: row.chapter,
      status: row.status,
      updated_at: new Date(row.updated_at),
    };
  }

  async updateSeries(
    id: string,
    input: UpdateSeriesInput
  ): Promise<Series | null> {
    const updates: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    if (input.name !== undefined) {
      updates.push(`name = $${paramIndex}`);
      params.push(input.name);
      paramIndex++;
    }

    if (input.chapter !== undefined) {
      updates.push(`chapter = $${paramIndex}`);
      params.push(input.chapter);
      paramIndex++;
    }

    if (input.status !== undefined) {
      updates.push(`status = $${paramIndex}`);
      params.push(input.status);
      paramIndex++;
    }

    if (updates.length === 0) {
      return this.getSeriesById(id);
    }

    updates.push(`updated_at = NOW()`);
    params.push(id);

    const query = `
      UPDATE series 
      SET ${updates.join(", ")}
      WHERE id = $${paramIndex}
      RETURNING id, name, chapter, status, updated_at
    `;

    const result = await this.sql(query, params);

    if (result.length === 0) {
      return null;
    }

    const row = result[0];
    return {
      id: row.id,
      name: row.name,
      chapter: row.chapter,
      status: row.status,
      updated_at: new Date(row.updated_at),
    };
  }

  async deleteSeries(id: string): Promise<boolean> {
    const query = "DELETE FROM series WHERE id = $1";
    const result = await this.sql(query, [id]);
    return result.length > 0;
  }

  async seriesExists(name: string, excludeId?: string): Promise<boolean> {
    let query = "SELECT 1 FROM series WHERE name = $1";
    const params: any[] = [name];

    if (excludeId) {
      query += " AND id != $2";
      params.push(excludeId);
    }

    const result = await this.sql(query, params);
    return result.length > 0;
  }
}
