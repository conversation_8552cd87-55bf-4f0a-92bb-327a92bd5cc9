import { neon } from "@neondatabase/serverless";
import type {
  Series,
  CreateSeriesInput,
  UpdateSeriesInput,
  PaginationQuery,
  PaginatedResponse,
} from "../types/series";

export class DatabaseService {
  private sql: ReturnType<typeof neon>;

  constructor(databaseUrl: string) {
    this.sql = neon(databaseUrl);
  }

  async getAllSeries(
    query: PaginationQuery
  ): Promise<PaginatedResponse<Series>> {
    const { page, limit, sort, order, status, search } = query;
    const offset = (page - 1) * limit;

    // Build dynamic queries based on filters
    let countResult;
    let data;

    if (status && search) {
      // Both status and search filters
      const searchPattern = `%${search}%`;
      countResult = await this.sql`
        SELECT COUNT(*) as count
        FROM series
        WHERE status = ${status} AND name ILIKE ${searchPattern}
      `;

      if (sort === 'name') {
        data = await this.sql`
          SELECT id, name, chapter, status, updated_at
          FROM series
          WHERE status = ${status} AND name ILIKE ${searchPattern}
          ORDER BY name ${order === 'asc' ? this.sql`ASC` : this.sql`DESC`}
          LIMIT ${limit} OFFSET ${offset}
        `;
      } else if (sort === 'chapter') {
        data = await this.sql`
          SELECT id, name, chapter, status, updated_at
          FROM series
          WHERE status = ${status} AND name ILIKE ${searchPattern}
          ORDER BY chapter ${order === 'asc' ? this.sql`ASC` : this.sql`DESC`}
          LIMIT ${limit} OFFSET ${offset}
        `;
      } else if (sort === 'status') {
        data = await this.sql`
          SELECT id, name, chapter, status, updated_at
          FROM series
          WHERE status = ${status} AND name ILIKE ${searchPattern}
          ORDER BY status ${order === 'asc' ? this.sql`ASC` : this.sql`DESC`}
          LIMIT ${limit} OFFSET ${offset}
        `;
      } else {
        data = await this.sql`
          SELECT id, name, chapter, status, updated_at
          FROM series
          WHERE status = ${status} AND name ILIKE ${searchPattern}
          ORDER BY updated_at ${order === 'asc' ? this.sql`ASC` : this.sql`DESC`}
          LIMIT ${limit} OFFSET ${offset}
        `;
      }
    } else if (status) {
      // Only status filter
      countResult = await this.sql`
        SELECT COUNT(*) as count
        FROM series
        WHERE status = ${status}
      `;

      if (sort === 'name') {
        data = await this.sql`
          SELECT id, name, chapter, status, updated_at
          FROM series
          WHERE status = ${status}
          ORDER BY name ${order === 'asc' ? this.sql`ASC` : this.sql`DESC`}
          LIMIT ${limit} OFFSET ${offset}
        `;
      } else if (sort === 'chapter') {
        data = await this.sql`
          SELECT id, name, chapter, status, updated_at
          FROM series
          WHERE status = ${status}
          ORDER BY chapter ${order === 'asc' ? this.sql`ASC` : this.sql`DESC`}
          LIMIT ${limit} OFFSET ${offset}
        `;
      } else if (sort === 'status') {
        data = await this.sql`
          SELECT id, name, chapter, status, updated_at
          FROM series
          WHERE status = ${status}
          ORDER BY status ${order === 'asc' ? this.sql`ASC` : this.sql`DESC`}
          LIMIT ${limit} OFFSET ${offset}
        `;
      } else {
        data = await this.sql`
          SELECT id, name, chapter, status, updated_at
          FROM series
          WHERE status = ${status}
          ORDER BY updated_at ${order === 'asc' ? this.sql`ASC` : this.sql`DESC`}
          LIMIT ${limit} OFFSET ${offset}
        `;
      }
    } else if (search) {
      // Only search filter
      const searchPattern = `%${search}%`;
      countResult = await this.sql`
        SELECT COUNT(*) as count
        FROM series
        WHERE name ILIKE ${searchPattern}
      `;

      if (sort === 'name') {
        data = await this.sql`
          SELECT id, name, chapter, status, updated_at
          FROM series
          WHERE name ILIKE ${searchPattern}
          ORDER BY name ${order === 'asc' ? this.sql`ASC` : this.sql`DESC`}
          LIMIT ${limit} OFFSET ${offset}
        `;
      } else if (sort === 'chapter') {
        data = await this.sql`
          SELECT id, name, chapter, status, updated_at
          FROM series
          WHERE name ILIKE ${searchPattern}
          ORDER BY chapter ${order === 'asc' ? this.sql`ASC` : this.sql`DESC`}
          LIMIT ${limit} OFFSET ${offset}
        `;
      } else if (sort === 'status') {
        data = await this.sql`
          SELECT id, name, chapter, status, updated_at
          FROM series
          WHERE name ILIKE ${searchPattern}
          ORDER BY status ${order === 'asc' ? this.sql`ASC` : this.sql`DESC`}
          LIMIT ${limit} OFFSET ${offset}
        `;
      } else {
        data = await this.sql`
          SELECT id, name, chapter, status, updated_at
          FROM series
          WHERE name ILIKE ${searchPattern}
          ORDER BY updated_at ${order === 'asc' ? this.sql`ASC` : this.sql`DESC`}
          LIMIT ${limit} OFFSET ${offset}
        `;
      }
    } else {
      // No filters
      countResult = await this.sql`SELECT COUNT(*) as count FROM series`;

      if (sort === 'name') {
        data = await this.sql`
          SELECT id, name, chapter, status, updated_at
          FROM series
          ORDER BY name ${order === 'asc' ? this.sql`ASC` : this.sql`DESC`}
          LIMIT ${limit} OFFSET ${offset}
        `;
      } else if (sort === 'chapter') {
        data = await this.sql`
          SELECT id, name, chapter, status, updated_at
          FROM series
          ORDER BY chapter ${order === 'asc' ? this.sql`ASC` : this.sql`DESC`}
          LIMIT ${limit} OFFSET ${offset}
        `;
      } else if (sort === 'status') {
        data = await this.sql`
          SELECT id, name, chapter, status, updated_at
          FROM series
          ORDER BY status ${order === 'asc' ? this.sql`ASC` : this.sql`DESC`}
          LIMIT ${limit} OFFSET ${offset}
        `;
      } else {
        data = await this.sql`
          SELECT id, name, chapter, status, updated_at
          FROM series
          ORDER BY updated_at ${order === 'asc' ? this.sql`ASC` : this.sql`DESC`}
          LIMIT ${limit} OFFSET ${offset}
        `;
      }
    }

    const total = parseInt((countResult as any)[0].count);
    const totalPages = Math.ceil(total / limit);

    return {
      data: (data as any).map((row: any) => ({
        id: row.id,
        name: row.name,
        chapter: row.chapter,
        status: row.status,
        updated_at: new Date(row.updated_at),
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  async getSeriesById(id: string): Promise<Series | null> {
    const result = await this.sql`
      SELECT id, name, chapter, status, updated_at
      FROM series
      WHERE id = ${id}
    `;

    if ((result as any).length === 0) {
      return null;
    }

    const row = result[0] as any;
    return {
      id: row.id,
      name: row.name,
      chapter: row.chapter,
      status: row.status,
      updated_at: new Date(row.updated_at),
    };
  }

  async createSeries(input: CreateSeriesInput): Promise<Series> {
    const result = await this.sql`
      INSERT INTO series (name, chapter, status)
      VALUES (${input.name}, ${input.chapter}, ${input.status})
      RETURNING id, name, chapter, status, updated_at
    `;

    const row = result[0] as any;

    return {
      id: row.id,
      name: row.name,
      chapter: row.chapter,
      status: row.status,
      updated_at: new Date(row.updated_at),
    };
  }

  async updateSeries(
    id: string,
    input: UpdateSeriesInput
  ): Promise<Series | null> {
    if (Object.keys(input).length === 0) {
      return this.getSeriesById(id);
    }

    let result;

    // Handle different combinations of updates
    if (input.name !== undefined && input.chapter !== undefined && input.status !== undefined) {
      result = await this.sql`
        UPDATE series
        SET name = ${input.name}, chapter = ${input.chapter}, status = ${input.status}, updated_at = NOW()
        WHERE id = ${id}
        RETURNING id, name, chapter, status, updated_at
      `;
    } else if (input.name !== undefined && input.chapter !== undefined) {
      result = await this.sql`
        UPDATE series
        SET name = ${input.name}, chapter = ${input.chapter}, updated_at = NOW()
        WHERE id = ${id}
        RETURNING id, name, chapter, status, updated_at
      `;
    } else if (input.name !== undefined && input.status !== undefined) {
      result = await this.sql`
        UPDATE series
        SET name = ${input.name}, status = ${input.status}, updated_at = NOW()
        WHERE id = ${id}
        RETURNING id, name, chapter, status, updated_at
      `;
    } else if (input.chapter !== undefined && input.status !== undefined) {
      result = await this.sql`
        UPDATE series
        SET chapter = ${input.chapter}, status = ${input.status}, updated_at = NOW()
        WHERE id = ${id}
        RETURNING id, name, chapter, status, updated_at
      `;
    } else if (input.name !== undefined) {
      result = await this.sql`
        UPDATE series
        SET name = ${input.name}, updated_at = NOW()
        WHERE id = ${id}
        RETURNING id, name, chapter, status, updated_at
      `;
    } else if (input.chapter !== undefined) {
      result = await this.sql`
        UPDATE series
        SET chapter = ${input.chapter}, updated_at = NOW()
        WHERE id = ${id}
        RETURNING id, name, chapter, status, updated_at
      `;
    } else if (input.status !== undefined) {
      result = await this.sql`
        UPDATE series
        SET status = ${input.status}, updated_at = NOW()
        WHERE id = ${id}
        RETURNING id, name, chapter, status, updated_at
      `;
    } else {
      return this.getSeriesById(id);
    }

    if ((result as any).length === 0) {
      return null;
    }

    const row = (result as any)[0];
    return {
      id: row.id,
      name: row.name,
      chapter: row.chapter,
      status: row.status,
      updated_at: new Date(row.updated_at),
    };
  }

  async deleteSeries(id: string): Promise<boolean> {
    // First check if the series exists
    const existingResult = await this.sql`
      SELECT id FROM series WHERE id = ${id}
    `;

    if ((existingResult as any).length === 0) {
      return false; // Series doesn't exist
    }

    // Delete the series
    await this.sql`DELETE FROM series WHERE id = ${id}`;

    // Verify deletion by checking if it still exists
    const verifyResult = await this.sql`
      SELECT id FROM series WHERE id = ${id}
    `;

    return (verifyResult as any).length === 0; // Return true if successfully deleted
  }

  async seriesExists(name: string, excludeId?: string): Promise<boolean> {
    let result;

    if (excludeId) {
      result = await this.sql`
        SELECT 1 FROM series
        WHERE name = ${name} AND id != ${excludeId}
      `;
    } else {
      result = await this.sql`
        SELECT 1 FROM series
        WHERE name = ${name}
      `;
    }

    return (result as any).length > 0;
  }
}
