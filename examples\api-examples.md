# API Usage Examples

Here are some examples of how to use the Series Management API:

## 1. Create a New Series

```bash
curl -X POST http://localhost:8787/api/series \
  -H "Content-Type: application/json" \
  -d '{
    "name": "One Piece",
    "chapter": 1095,
    "status": "Reading"
  }'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "One Piece",
    "chapter": 1095,
    "status": "Reading",
    "updated_at": "2024-01-15T10:30:00.000Z"
  },
  "message": "Series created successfully"
}
```

## 2. Get All Series with Pagination

```bash
curl "http://localhost:8787/api/series?page=1&limit=5&sort=name&order=asc"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "Attack on Titan",
        "chapter": 139,
        "status": "Completed",
        "updated_at": "2024-01-15T10:30:00.000Z"
      },
      {
        "id": "123e4567-e89b-12d3-a456-426614174001",
        "name": "Naruto",
        "chapter": 700,
        "status": "Completed",
        "updated_at": "2024-01-15T10:30:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 5,
      "total": 25,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

## 3. Search Series by Name

```bash
curl "http://localhost:8787/api/series?search=naruto&limit=10"
```

## 4. Filter by Status

```bash
curl "http://localhost:8787/api/series?status=Reading&page=1&limit=10"
```

## 5. Get Series by ID

```bash
curl "http://localhost:8787/api/series/123e4567-e89b-12d3-a456-426614174000"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "One Piece",
    "chapter": 1095,
    "status": "Reading",
    "updated_at": "2024-01-15T10:30:00.000Z"
  }
}
```

## 6. Update a Series

```bash
curl -X PUT http://localhost:8787/api/series/123e4567-e89b-12d3-a456-426614174000 \
  -H "Content-Type: application/json" \
  -d '{
    "chapter": 1096,
    "status": "Reading"
  }'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "One Piece",
    "chapter": 1096,
    "status": "Reading",
    "updated_at": "2024-01-15T11:30:00.000Z"
  },
  "message": "Series updated successfully"
}
```

## 7. Delete a Series

```bash
curl -X DELETE http://localhost:8787/api/series/123e4567-e89b-12d3-a456-426614174000
```

**Response:**
```json
{
  "success": true,
  "message": "Series deleted successfully"
}
```

## 8. Error Examples

### Validation Error (400)
```bash
curl -X POST http://localhost:8787/api/series \
  -H "Content-Type: application/json" \
  -d '{
    "name": "",
    "chapter": -5
  }'
```

**Response:**
```json
{
  "success": false,
  "error": "Invalid request data",
  "data": [
    {
      "code": "too_small",
      "minimum": 1,
      "type": "string",
      "inclusive": true,
      "exact": false,
      "message": "Name is required",
      "path": ["name"]
    }
  ]
}
```

### Not Found Error (404)
```bash
curl "http://localhost:8787/api/series/non-existent-id"
```

**Response:**
```json
{
  "success": false,
  "error": "Series not found"
}
```

### Duplicate Name Error (409)
```bash
curl -X POST http://localhost:8787/api/series \
  -H "Content-Type: application/json" \
  -d '{
    "name": "One Piece"
  }'
```

**Response:**
```json
{
  "success": false,
  "error": "Series with this name already exists"
}
```

## JavaScript/TypeScript Example

```typescript
// Example using fetch API
const API_BASE = 'http://localhost:8787/api';

// Create a new series
async function createSeries(seriesData) {
  const response = await fetch(`${API_BASE}/series`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(seriesData),
  });
  
  return await response.json();
}

// Get all series with pagination
async function getAllSeries(page = 1, limit = 10, filters = {}) {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...filters,
  });
  
  const response = await fetch(`${API_BASE}/series?${params}`);
  return await response.json();
}

// Usage
const newSeries = await createSeries({
  name: "Dragon Ball",
  chapter: 519,
  status: "Completed"
});

const seriesList = await getAllSeries(1, 20, {
  status: "Reading",
  sort: "name",
  order: "asc"
});
```
