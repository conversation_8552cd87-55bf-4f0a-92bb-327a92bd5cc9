{"name": "hono-test", "type": "module", "scripts": {"start": "vite", "build": "vite build", "preview": "$npm_execpath run build && vite preview", "deploy": "$npm_execpath run build && wrangler deploy", "cf-typegen": "wrangler types --env-interface CloudflareBindings"}, "dependencies": {"@neondatabase/serverless": "^1.0.1", "hono": "^4.7.11"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.2.3", "vite": "^6.3.5", "vite-ssr-components": "^0.2.0", "wrangler": "^4.17.0"}}